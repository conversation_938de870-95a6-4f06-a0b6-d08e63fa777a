package com.klosesoft.billingsolution.domain.logic.service.billing.mapping

import com.klosesoft.billingsolution.domain.logic.service.payment.EpcQrCodeGeneratorService
import com.klosesoft.billingsolution.domain.model.valueobject.Aggregation
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.CustomerType
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.DocumentItemMode
import com.klosesoft.billingsolution.domain.model.valueobject.DocumentType
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.TranslationType
import com.klosesoft.billingsolution.persistence.model.entity.Address
import com.klosesoft.billingsolution.persistence.model.entity.Document
import com.klosesoft.billingsolution.persistence.model.entity.DocumentItem
import com.klosesoft.billingsolution.persistence.model.entity.payment.PaymentAccount
import com.klosesoft.billingsolution.persistence.service.BusinessSegmentLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.DocumentLoadService
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.PaymentAccountLoadService
import io.github.oshai.kotlinlogging.KotlinLogging
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.Locale

@Service
class VelocityWheelsDocumentToPdfMapping(
    private val translateService: TranslateService,
    private val customerLoadService: CustomerLoadService,
    private val epcQrCodeGeneratorService: EpcQrCodeGeneratorService,
    private val paymentAccountLoadService: PaymentAccountLoadService,
    private val businessSegmentLoadService: BusinessSegmentLoadService,
    private val orderLoadService: OrderLoadService,
    private val documentLoadService: DocumentLoadService,
) : DocumentToPdfMapping {
    private val logger = KotlinLogging.logger {}

    override suspend fun getTemplate(
        documentType: DocumentType,
    ): String {
        when (documentType) {
            DocumentType.DEPOSIT, DocumentType.FINAL, DocumentType.REVERSED -> {
                return "invoice_velocity_wheels.jrxml"
            }

            else -> throw IllegalArgumentException("$documentType is unknown for this mapping")
        }
    }

    override suspend fun getParameters(
        document: Document,
        language: Language,
    ): Map<String, Any> {
        val order = orderLoadService.findById(document.orderId)
        val customer = customerLoadService.findById(order.customerId)
        val businessSegment = businessSegmentLoadService.findById(order.businessSegmentId)

        val parameters = mutableMapOf<String, Any>()

        // Basic document information
        parameters["documentNumber"] = document.documentNumber
        parameters["documentDate"] = document.documentDate.toString()
        parameters["orderKey"] = order.key
        parameters["currency"] = document.currency.name

        // Company information
        parameters["companyName"] = "Velocity Wheels Ltd."
        parameters["companyAddress"] = "Speed Avenue 42, London SW1A 1AA, UK"
        parameters["companyPhone"] = "+44 20 7946 0958"
        parameters["companyEmail"] = "<EMAIL>"
        parameters["companyVat"] = businessSegment.vatId
        parameters["companyLogo"] = businessSegment.logo

        // Customer information
        when (customer.customerType) {
            CustomerType.PERSON -> {
                parameters["customerName"] = "${customer.firstName} ${customer.lastName}"
            }
            CustomerType.COMPANY -> {
                parameters["customerName"] = customer.companyName ?: ""
            }
        }

        if (customer.vatId != null) {
            parameters["customerVat"] = customer.vatId!!
        }

        // Payment information
        val paymentAccounts = paymentAccountLoadService.findByTenantIdAndType(
            document.tenantId,
            PaymentAccountType.BANK_ACCOUNT
        )
        
        if (paymentAccounts.isNotEmpty()) {
            val defaultAccount = paymentAccounts.first { it.defaultAccount }
            parameters["bankAccount"] = defaultAccount.accountId
            parameters["bankName"] = defaultAccount.name
            parameters["bic"] = defaultAccount.bic
        }

        // Document items
        val documentItems = documentLoadService.findDocumentItemsByDocumentId(document.id)
        val itemDataSource = JRBeanCollectionDataSource(
            documentItems.map { item ->
                mapOf(
                    "description" to item.name,
                    "quantity" to item.quantity,
                    "unitPrice" to item.unitNetAmount,
                    "totalPrice" to item.unitNetAmount.multiply(item.quantity),
                    "taxRate" to (item.taxes.firstOrNull()?.taxRate ?: BigDecimal.ZERO),
                )
            }
        )
        parameters["itemDataSource"] = itemDataSource

        // Totals
        val netTotal = documentItems
            .filter { it.debitCreditIndicator == DebitCreditIndicator.DEBIT }
            .sumOf { it.unitNetAmount.multiply(it.quantity) }
        
        val taxTotal = documentItems
            .filter { it.debitCreditIndicator == DebitCreditIndicator.DEBIT }
            .sumOf { item ->
                val taxRate = item.taxes.firstOrNull()?.taxRate ?: BigDecimal.ZERO
                item.unitNetAmount.multiply(item.quantity).multiply(taxRate).divide(BigDecimal(100))
            }

        parameters["netTotal"] = netTotal
        parameters["taxTotal"] = taxTotal
        parameters["grossTotal"] = netTotal.add(taxTotal)

        // Translations
        parameters["titleLabel"] = translateService.translate(
            document.tenantId,
            language,
            TranslationType.GENERAL_DOCUMENT,
            "DOCUMENT_TITLE",
            emptyMap()
        )

        parameters["customerLabel"] = "Customer"
        parameters["dateLabel"] = "Date"
        parameters["orderLabel"] = "Order"
        parameters["descriptionLabel"] = "Description"
        parameters["quantityLabel"] = "Quantity"
        parameters["unitPriceLabel"] = "Unit Price"
        parameters["totalPriceLabel"] = "Total Price"
        parameters["netTotalLabel"] = "Net Total"
        parameters["taxLabel"] = "VAT"
        parameters["grossTotalLabel"] = "Gross Total"
        parameters["paymentInfoLabel"] = "Payment Information"
        parameters["thankYouLabel"] = "Thank you for choosing Velocity Wheels!"

        // EPC QR Code for payment
        if (paymentAccounts.isNotEmpty()) {
            val defaultAccount = paymentAccounts.first { it.defaultAccount }
            val epcQrCode = epcQrCodeGeneratorService.generateEpcQrCode(
                beneficiaryName = defaultAccount.accountHolder,
                beneficiaryAccount = defaultAccount.accountId,
                amount = netTotal.add(taxTotal),
                currency = document.currency,
                remittanceInformation = "Invoice ${document.documentNumber}",
                beneficiaryBic = defaultAccount.bic
            )
            parameters["epcQrCode"] = epcQrCode
        }

        return parameters
    }

    override suspend fun getLocale(
        document: Document,
        language: Language,
    ): Locale {
        return when (language) {
            Language.EN -> Locale.UK
            Language.DE -> Locale.GERMANY
            else -> Locale.UK
        }
    }
}
