package com.klosesoft.billingsolution.domain.logic.service.billing.mapping

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.domain.logic.service.payment.EpcQrCodeGeneratorService
import com.klosesoft.billingsolution.persistence.service.BusinessSegmentLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.DocumentLoadService
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.PaymentAccountLoadService
import org.springframework.stereotype.Service

@Service
class DocumentMappingService(
    private val translateService: TranslateService,
    private val customerLoadService: CustomerLoadService,
    private val epcQrCodeGeneratorService: EpcQrCodeGeneratorService,
    private val paymentAccountLoadService: PaymentAccountLoadService,
    private val businessSegmentLoadService: BusinessSegmentLoadService,
    private val orderLoadService: OrderLoadService,
    private val documentLoadService: DocumentLoadService,
) {

    fun getMapping(
        tenantKey: String,
    ): DocumentToPdfMapping = when (tenantKey) {
        BillingSolutionConstants.TENANT_KEY_BIKESALE_UK ->
            BikeSaleUkDocumentToPdfMapping(
                translateService,
                customerLoadService,
                epcQrCodeGeneratorService,
                paymentAccountLoadService,
                businessSegmentLoadService,
                orderLoadService,
                documentLoadService,
            )

        BillingSolutionConstants.TENANT_KEY_MVZ_GESUND ->
            MvzGesundDocumentToPdfMapping(
                translateService,
                customerLoadService,
                epcQrCodeGeneratorService,
                paymentAccountLoadService,
                businessSegmentLoadService,
                orderLoadService,
                documentLoadService,
            )

        BillingSolutionConstants.TENANT_KEY_SUNPOWER ->
            SunpowerDocumentToPdfMapping(
                translateService,
                customerLoadService,
                epcQrCodeGeneratorService,
                paymentAccountLoadService,
                businessSegmentLoadService,
                orderLoadService,
                documentLoadService,
            )

        BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS ->
            VelocityWheelsDocumentToPdfMapping(
                translateService,
                customerLoadService,
                epcQrCodeGeneratorService,
                paymentAccountLoadService,
                businessSegmentLoadService,
                orderLoadService,
                documentLoadService,
            )

        else -> {
            throw IllegalArgumentException("mapping for tenant $tenantKey not yet implemented")
        }
    }
}
