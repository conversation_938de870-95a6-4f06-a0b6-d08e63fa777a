package com.klosesoft.billingsolution.databasefiller.tenant

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_ID
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_KEY
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.databasefiller.creator.BookkeepingCreator
import com.klosesoft.billingsolution.databasefiller.creator.CustomerCreator
import com.klosesoft.billingsolution.databasefiller.creator.NotificationReceiverCreator
import com.klosesoft.billingsolution.databasefiller.creator.PaymentCreator
import com.klosesoft.billingsolution.databasefiller.creator.ReportConfigCreator
import com.klosesoft.billingsolution.databasefiller.creator.SubscriptionCreator
import com.klosesoft.billingsolution.databasefiller.creator.TenantCreator
import com.klosesoft.billingsolution.databasefiller.creator.TranslationCreator
import com.klosesoft.billingsolution.domain.logic.api.job.report.ReportJob
import com.klosesoft.billingsolution.domain.logic.service.billing.TranslationService
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingAccountService
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingRuleService
import com.klosesoft.billingsolution.domain.logic.service.common.AddressService
import com.klosesoft.billingsolution.domain.logic.service.common.BusinessSegmentService
import com.klosesoft.billingsolution.domain.logic.service.common.CustomerService
import com.klosesoft.billingsolution.domain.logic.service.common.NotificationApiReceiverService
import com.klosesoft.billingsolution.domain.logic.service.common.SubscriptionService
import com.klosesoft.billingsolution.domain.logic.service.common.TenantService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentAccountService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentCategorizationRuleService
import com.klosesoft.billingsolution.domain.logic.service.user.RoleService
import com.klosesoft.billingsolution.domain.logic.service.user.UserService
import com.klosesoft.billingsolution.domain.model.dto.AddressDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.BookingAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingRuleType
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.domain.model.valueobject.GeneralDocumentTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentCategory
import com.klosesoft.billingsolution.domain.model.valueobject.ReportFormat
import com.klosesoft.billingsolution.domain.model.valueobject.ReportInterval
import com.klosesoft.billingsolution.domain.model.valueobject.ReportType
import com.klosesoft.billingsolution.domain.model.valueobject.Right
import com.klosesoft.billingsolution.persistence.model.entity.Customer
import com.klosesoft.billingsolution.persistence.model.entity.Role
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.RoleLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UniqueIdGeneratorService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import com.klosesoft.billingsolution.workflow.WorkflowDeploymentService
import org.springframework.core.env.Environment
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Component
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Locale
import java.util.UUID

@Component
class VelocityWheelsInitializer(
    private val tenantService: TenantService,
    private val tenantLoadService: TenantLoadService,
    private val customerLoadService: CustomerLoadService,
    private val businessSegmentService: BusinessSegmentService,
    private val userService: UserService,
    private val userLoadService: UserLoadService,
    private val customerService: CustomerService,
    private val environment: Environment,
    private val bookingAccountService: BookingAccountService,
    private val bookingRuleService: BookingRuleService,
    private val roleService: RoleService,
    private val roleLoadService: RoleLoadService,
    private val uniqueIdGeneratorService: UniqueIdGeneratorService,
    private val paymentAccountService: PaymentAccountService,
    private val paymentCategorizationRuleService: PaymentCategorizationRuleService,
    private val reportConfigCreator: ReportConfigCreator,
    private val reportJob: ReportJob,
    private val translationService: TranslationService,
    private val resourceLoader: ResourceLoader,
    private val addressService: AddressService,
    private val notificationApiReceiverService: NotificationApiReceiverService,
    private val subscriptionService: SubscriptionService,
    private val workflowDeploymentService: WorkflowDeploymentService,
) {

    private companion object {
        const val PREMIUM_LEASING = "PREMIUM_LEASING"
        const val ECONOMY_LEASING = "ECONOMY_LEASING"
    }

    suspend fun setupTenant() {
        createTenant()
        createBusinessSegments()
        createTenantWebPortalUsers()
        createBookingkeepingConfig()
        createPaymentConfig()
        createTranslations()

        if (environment.activeProfiles.contains("demo")) {
            addCustomers()
            addSubscriptions()
            createReports()
        }

        if (environment.activeProfiles.contains("cucumber")) {
            createCucumberNotificationApiReceiver()
        }
    }

    private suspend fun createReports() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id
        reportConfigCreator.createConfig(tenantId, ReportType.PAYMENT, ReportFormat.JSON, ReportInterval.DAILY, "UTC")
        reportConfigCreator.createConfig(tenantId, ReportType.SUBLEDGER, ReportFormat.JSON, ReportInterval.DAILY, "UTC")

        reportJob.generateSubledgerReport()
        reportJob.generatePaymentReport()
        reportJob.generateApprovalReport()
    }

    private suspend fun createTenantWebPortalUsers() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id

        createRoles(tenantId)
        createUserRoles(tenantId)
        createRoleRights(tenantId)
    }

    private suspend fun createRoles(
        tenantId: UUID,
    ) {
        val adminRole = Role(
            key = "ADMIN",
            name = "Administrator",
            tenantId = tenantId,
            createdBy = SYSTEM_USER_ID,
            lastModifiedBy = SYSTEM_USER_ID,
        )

        val userRole = Role(
            key = "USER",
            name = "User",
            tenantId = tenantId,
            createdBy = SYSTEM_USER_ID,
            lastModifiedBy = SYSTEM_USER_ID,
        )

        roleService.createRole(adminRole)
        roleService.createRole(userRole)
    }

    private suspend fun createUserRoles(
        tenantId: UUID,
    ) {
        val adminRole = roleLoadService.findByTenantIdAndKey(tenantId, "ADMIN")
        val userRole = roleLoadService.findByTenantIdAndKey(tenantId, "USER")

        val paulKloseUserId = userLoadService.fetchUserId("<EMAIL>")
        val florianFittkauUserId = userLoadService.fetchUserId("<EMAIL>")
        val julianZarubaUserId = userLoadService.fetchUserId("<EMAIL>")

        userService.assignRoleToUser(paulKloseUserId, adminRole.id, SYSTEM_USER_ID)
        userService.assignRoleToUser(florianFittkauUserId, userRole.id, SYSTEM_USER_ID)
        userService.assignRoleToUser(julianZarubaUserId, userRole.id, SYSTEM_USER_ID)
    }

    private suspend fun createRoleRights(
        tenantId: UUID,
    ) {
        val adminRole = roleLoadService.findByTenantIdAndKey(tenantId, "ADMIN")
        val userRole = roleLoadService.findByTenantIdAndKey(tenantId, "USER")

        // Admin gets all rights
        val allRights = Right.entries
        allRights.forEach { right ->
            roleService.assignRightToRole(adminRole.id, right, SYSTEM_USER_ID)
        }

        // User gets limited rights
        val userRights = listOf(
            Right.CUSTOMERS_READ,
            Right.ORDERS_READ,
            Right.DOCUMENTS_READ,
            Right.SUBSCRIPTIONS_READ,
            Right.SUBSCRIPTIONS_WRITE,
        )
        userRights.forEach { right ->
            roleService.assignRightToRole(userRole.id, right, SYSTEM_USER_ID)
        }
    }

    private suspend fun createTranslations() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id

        translationService.createTranslation(
            TranslationCreator.createTranslation(
                tenantId = tenantId,
                translationKey = GeneralDocumentTranslationKey.DOCUMENT_TITLE,
                language = Language.EN,
                translation = "Vehicle Lease Agreement",
            ),
        )

        translationService.createTranslation(
            TranslationCreator.createTranslation(
                tenantId = tenantId,
                translationKey = GeneralDocumentTranslationKey.DOCUMENT_TITLE,
                language = Language.DE,
                translation = "Fahrzeug-Leasingvertrag",
            ),
        )
    }

    private suspend fun createBookingkeepingConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)

        createBookingAccounts(tenantId)
        createBookingRules(tenantId)
    }

    private suspend fun createBookingAccounts(
        tenantId: UUID,
    ) {
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = "1000",
                name = "Cash and Bank",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = "1200",
                name = "Accounts Receivable",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = "4000",
                name = "Leasing Revenue",
                bookingAccountType = BookingAccountType.REVENUE,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = "2000",
                name = "Accounts Payable",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = "1760",
                name = "VAT Input",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )

        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = "3806",
                name = "VAT Output",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
    }

    private suspend fun createBookingRules(
        tenantId: UUID,
    ) {
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Leasing Revenue Rule",
                bookingRuleType = BookingRuleType.REVENUE,
                debitAccountNumber = "1200",
                creditAccountNumber = "4000",
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
            ),
        )

        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "VAT Rule",
                bookingRuleType = BookingRuleType.TAX,
                debitAccountNumber = "1760",
                creditAccountNumber = "3806",
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
            ),
        )
    }

    private suspend fun createBusinessSegments() {
        val logo = getLogo()

        val originatorAddress = addressService.createAddress(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            AddressDomainDto(
                UUID.randomUUID().toString(),
                companyName = "Velocity Wheels Ltd.",
                street = "Speed Avenue",
                houseNumber = "42",
                city = "London",
                country = "UK",
                postalCode = "SW1A 1AA",
                mailAddress = "",
            ),
        )

        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_VELOCITY_WHEELS).id
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantId,
                PREMIUM_LEASING,
                "'VW-PREM-'&\$year&'-'&\$numberRange(10000,99999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB987654321",
            ),
        )

        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantId,
                ECONOMY_LEASING,
                "'VW-ECO-'&\$year&'-'&\$numberRange(10000,99999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB987654321",
            ),
        )
    }

    protected suspend fun createTenant() {
        val tenantVelocityWheels = tenantService.createTenant(TenantCreator.createActiveTenant(TENANT_KEY_VELOCITY_WHEELS))
        tenantService.createTenantConfig(
            TenantCreator.createTenantConfig(
                tenantId = tenantVelocityWheels.id,
                appClientId = "4vw12wheels34lease56789abc",
                orderWorkflow = BillingSolutionConstants.VELOCITY_WHEELS_WORKFLOW,
                ledgerCurrency = Currency.GBP,
                features = listOf(Feature.ENABLE_BALANCE_CASE),
                locale = Locale.UK,
                theme = "velocity-wheels",
                timeZone = "UTC",
                defaultLanguage = Language.EN,
                defaultTaxRate = BigDecimal("20.00"),
            ),
        )

        workflowDeploymentService.deployWorkflows()
    }

    private suspend fun createPaymentConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)

        createPaymentAccounts(tenantId)
        createPaymentCategorizationRule(tenantId)
    }

    private suspend fun createPaymentAccounts(
        tenantId: UUID,
    ) {
        paymentAccountService.createPaymentAccount(
            PaymentCreator.createPaymentAccount(
                tenantId = tenantId,
                accountId = "*****************",
                name = "Velocity Wheels Main Account",
                description = "Primary account for lease payments",
                bic = "VWHEELSB",
                accountHolder = "Velocity Wheels Ltd.",
                paymentAccountType = PaymentAccountType.BANK_ACCOUNT,
                defaultAccount = true,
            ),
        )
    }

    private suspend fun createPaymentCategorizationRule(
        tenantId: UUID,
    ) {
        paymentCategorizationRuleService.createPaymentCategorizationRule(
            tenantId = tenantId,
            name = "Lease Payment Rule",
            description = "Categorizes incoming lease payments",
            paymentCategory = PaymentCategory.LEASE_PAYMENT,
            createdBy = SYSTEM_USER_ID,
        )
    }

    private suspend fun createCucumberNotificationApiReceiver() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)

        notificationApiReceiverService.createNotificationApiReceiver(
            tenantId,
            SYSTEM_USER_KEY,
            NotificationReceiverCreator.createCucumberNotificationReceiver(),
        )
    }

    private suspend fun getLogo(): ByteArray {
        return try {
            val resource = resourceLoader.getResource("classpath:logo/velocity-wheels-logo.png")
            resource.inputStream.readAllBytes()
        } catch (e: IOException) {
            // Fallback to a simple placeholder
            "VELOCITY_WHEELS_LOGO".toByteArray()
        }
    }

    private suspend fun addCustomers() {
        // Premium customers
        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany(
                key = "Luxury_Motors_Ltd",
                companyName = "Luxury Motors Ltd",
                language = Language.EN,
                city = "London",
                country = "UK",
                vat = "GB123456789",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                key = "James_Bond",
                firstName = "James",
                lastName = "Bond",
                language = Language.EN,
                city = "London",
                country = "UK",
            ),
        )

        // Economy customers
        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany(
                key = "Budget_Fleet_Services",
                companyName = "Budget Fleet Services",
                language = Language.EN,
                city = "Manchester",
                country = "UK",
                vat = "GB987654321",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                key = "Sarah_Connor",
                firstName = "Sarah",
                lastName = "Connor",
                language = Language.EN,
                city = "Birmingham",
                country = "UK",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                key = "Michael_Knight",
                firstName = "Michael",
                lastName = "Knight",
                language = Language.EN,
                city = "Edinburgh",
                country = "UK",
            ),
        )
    }

    private suspend fun addSubscriptions() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_VELOCITY_WHEELS)
        val now = TimeProvider.nowDateInTimeZone(tenantLoadService.findTenantConfigByTenantId(tenantId).getTimeZoneAsObject())
        val currency = Currency.GBP

        // Premium leasing subscriptions
        val luxuryMotors = customerLoadService.findByTenantIdAndKey(tenantId, "Luxury_Motors_Ltd")
        addPremiumSubscriptions(
            luxuryMotors,
            now,
            currency,
        )

        val jamesBond = customerLoadService.findByTenantIdAndKey(tenantId, "James_Bond")
        addPremiumSubscriptions(
            jamesBond,
            now,
            currency,
        )

        // Economy leasing subscriptions
        val budgetFleet = customerLoadService.findByTenantIdAndKey(tenantId, "Budget_Fleet_Services")
        addEconomySubscriptions(
            budgetFleet,
            now,
            currency,
        )

        val sarahConnor = customerLoadService.findByTenantIdAndKey(tenantId, "Sarah_Connor")
        addEconomySubscriptions(
            sarahConnor,
            now,
            currency,
        )

        val michaelKnight = customerLoadService.findByTenantIdAndKey(tenantId, "Michael_Knight")
        addEconomySubscriptions(
            michaelKnight,
            now,
            currency,
        )
    }

    private suspend fun addPremiumSubscriptions(
        customer: Customer,
        startDate: LocalDate,
        currency: Currency,
    ) {
        // BMW X5 Monthly Lease
        subscriptionService.createSubscription(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createMonthlyCarLeaseSubscription(
                key = "${customer.key}_BMW_X5_LEASE",
                customerKey = customer.key,
                vehicleModel = "BMW X5 M50i",
                monthlyAmount = BigDecimal("899.00"),
                currency = currency,
                startDate = startDate.minusDays(30),
                endDate = startDate.plusYears(3),
            ),
        )

        // Maintenance package
        subscriptionService.createSubscription(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createMaintenanceSubscription(
                key = "${customer.key}_BMW_X5_MAINTENANCE",
                customerKey = customer.key,
                vehicleModel = "BMW X5 M50i",
                monthlyAmount = BigDecimal("149.00"),
                currency = currency,
                startDate = startDate.minusDays(30),
                endDate = startDate.plusYears(3),
            ),
        )

        // Insurance package
        subscriptionService.createSubscription(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createInsuranceSubscription(
                key = "${customer.key}_BMW_X5_INSURANCE",
                customerKey = customer.key,
                vehicleModel = "BMW X5 M50i",
                monthlyAmount = BigDecimal("89.00"),
                currency = currency,
                startDate = startDate.minusDays(30),
                endDate = startDate.plusYears(3),
            ),
        )
    }

    private suspend fun addEconomySubscriptions(
        customer: Customer,
        startDate: LocalDate,
        currency: Currency,
    ) {
        // Ford Focus Monthly Lease
        subscriptionService.createSubscription(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createMonthlyCarLeaseSubscription(
                key = "${customer.key}_FORD_FOCUS_LEASE",
                customerKey = customer.key,
                vehicleModel = "Ford Focus Titanium",
                monthlyAmount = BigDecimal("299.00"),
                currency = currency,
                startDate = startDate.minusDays(15),
                endDate = startDate.plusYears(2),
            ),
        )

        // Basic maintenance package
        subscriptionService.createSubscription(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createMaintenanceSubscription(
                key = "${customer.key}_FORD_FOCUS_MAINTENANCE",
                customerKey = customer.key,
                vehicleModel = "Ford Focus Titanium",
                monthlyAmount = BigDecimal("79.00"),
                currency = currency,
                startDate = startDate.minusDays(15),
                endDate = startDate.plusYears(2),
            ),
        )

        // Basic insurance package
        subscriptionService.createSubscription(
            TENANT_KEY_VELOCITY_WHEELS,
            SYSTEM_USER_KEY,
            SubscriptionCreator.createInsuranceSubscription(
                key = "${customer.key}_FORD_FOCUS_INSURANCE",
                customerKey = customer.key,
                vehicleModel = "Ford Focus Titanium",
                monthlyAmount = BigDecimal("49.00"),
                currency = currency,
                startDate = startDate.minusDays(15),
                endDate = startDate.plusYears(2),
            ),
        )

        // Add a weekly lease for some variety
        if (customer.key == "Michael_Knight") {
            subscriptionService.createSubscription(
                TENANT_KEY_VELOCITY_WHEELS,
                SYSTEM_USER_KEY,
                SubscriptionCreator.createWeeklyCarLeaseSubscription(
                    key = "${customer.key}_KITT_WEEKLY_LEASE",
                    customerKey = customer.key,
                    vehicleModel = "Pontiac Trans Am (KITT)",
                    weeklyAmount = BigDecimal("199.00"),
                    currency = currency,
                    startDate = startDate.minusDays(7),
                    endDate = startDate.plusMonths(6),
                ),
            )
        }
    }
}
